﻿using UnityEngine;
using DG.Tweening;

public class TouchController : MonoBehaviour
{
    public float holdTimeThreshold = 0.5f;
    public float rotationSpeed = 0.1f;
    private bool isDragging = false;
    private Vector2 dragLastPosition;
    private float holdTimer = 0f;
    private GameObject currentStemObject = null;
    private bool canRotateThisTouchSession = true; // Bu dokunma seansında döndürme yapılabilir mi?

    // Referanslar
    CornBasin basinController;
    AudioManager audioM;
    CornGenerator cornGeneratorInstance;

    private void Start()
    {
        basinController = CornBasin.Instance;
        audioM = AudioManager.Instance;
        cornGeneratorInstance = CornGenerator.Instance;
    }

    void Update()
    {
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);
            HandleTouch(touch.phase, touch.position);
        }
        else if (Input.GetMouseButtonDown(0))
        {
            HandleTouch(TouchPhase.Began, Input.mousePosition);
        }
        else if (Input.GetMouseButton(0))
        {
            HandleTouch(TouchPhase.Moved, Input.mousePosition);
        }
        else if (Input.GetMouseButtonUp(0))
        {
            HandleTouch(TouchPhase.Ended, Input.mousePosition);
        }
    }

    void HandleTouch(TouchPhase phase, Vector2 screenPosition)
    {
        switch (phase)
        {
            case TouchPhase.Began:
                {
                    currentStemObject = null;
                    isDragging = false;
                    holdTimer = 0f;
                    dragLastPosition = screenPosition;
                    canRotateThisTouchSession = true; // Her yeni dokunmada varsayılan olarak döndürme mümkün

                    Ray ray = Camera.main.ScreenPointToRay(screenPosition);
                    RaycastHit hit;

                    if (Physics.Raycast(ray, out hit))
                    {
                        if (hit.collider.CompareTag("CornKernel"))
                        {
                            if (hit.collider.enabled)
                            {
                                PeelCornKernel(hit.collider.gameObject);
                                Debug.Log("CornKernel, Tıklama sırasında tetiklendi. Döndürme bu seans için engellendi.");
                                canRotateThisTouchSession = false; // İlk dokunuş kernel ise bu seans boyunca döndürme yok
                            }
                        }
                        else if (hit.collider.CompareTag("Stem"))
                        {
                            currentStemObject = hit.collider.gameObject;
                            // Stem'e dokunuldu ama kernel'a değil, döndürme hala mümkün.
                        }
                    }
                }
                break;

            case TouchPhase.Moved:
                {
                    holdTimer += Time.deltaTime;

                    if (!isDragging && holdTimer >= holdTimeThreshold)
                    {
                        isDragging = true;
                    }

                    // Sadece döndürme izni varsa, sürükleniyorsa VE mısır animasyonda değilse
                    if (isDragging && canRotateThisTouchSession && cornGeneratorInstance != null && !cornGeneratorInstance.IsAnimating) 
                    {
                        if (currentStemObject != null)
                        {
                            float deltaX = screenPosition.x - dragLastPosition.x;
                            if (Mathf.Abs(deltaX) > Mathf.Epsilon)
                            {
                                float rotationAmount = -deltaX * rotationSpeed;
                                currentStemObject.transform.parent.Rotate(Vector3.up, rotationAmount, Space.World);
                            }
                        }
                        else // Sap seçili değilse ve döndürme izni varsa, genel mısırı döndür
                        {
                            if (cornGeneratorInstance != null && cornGeneratorInstance.currentCorn != null)
                            {
                                Transform cornTransform = cornGeneratorInstance.currentCorn.transform;
                                float deltaX = screenPosition.x - dragLastPosition.x;
                                float deltaY = screenPosition.y - dragLastPosition.y;

                                if (Mathf.Abs(deltaX) > Mathf.Epsilon)
                                {
                                    cornTransform.Rotate(Vector3.up, -deltaX * rotationSpeed, Space.World);
                                }
                                if (Mathf.Abs(deltaY) > Mathf.Epsilon)
                                {
                                    cornTransform.Rotate(Vector3.right, deltaY * rotationSpeed, Space.World);
                                }
                            }
                        }
                        dragLastPosition = screenPosition;
                    }
                    else // Döndürme izni yoksa veya henüz sürükleme başlamadıysa, kernel topla
                    {
                        Ray ray = Camera.main.ScreenPointToRay(screenPosition);
                        RaycastHit hit;
                        if (Physics.Raycast(ray, out hit))
                        {
                            if (hit.collider.CompareTag("CornKernel") && hit.collider.enabled)
                            {
                                PeelCornKernel(hit.collider.gameObject);
                                Debug.Log("CornKernel, sürükleme sırasında (döndürme engelli veya sürükleme başlamadı) tetiklendi.");
                            }
                        }
                    }
                }
                break;

            case TouchPhase.Ended:
            case TouchPhase.Canceled:
                {
                    isDragging = false;
                    currentStemObject = null;
                    holdTimer = 0f;
                    canRotateThisTouchSession = true; // Bir sonraki dokunma için sıfırla
                }
                break;
        }
    }

    void PeelCornKernel(GameObject cornKernel)
    {
        if (cornKernel == null || !cornKernel.GetComponent<Collider>().enabled) return;

        // Ses çal - artık çakışma kontrolü sadece loop sesler için yapılıyor
        audioM.PlaySoundFromPool("BubbleBrust", false);

        Collider col = cornKernel.GetComponent<Collider>();
        if (col != null)
            col.enabled = false;

        Vector3 targetPos = basinController.targetTransform.position;
        Vector3 originalPosition = cornKernel.transform.position;

        // Optimize edilmiş animasyon - daha az Sequence kullanımı
        // İlk zıplama ve döndürme
        cornKernel.transform.DOJump(originalPosition, 2f, 1, 1f)
            .SetEase(Ease.OutQuad)
            .OnComplete(() => {
                // Zıplama bitince hedef pozisyona git ve büyüt
                cornKernel.transform.DOMove(targetPos, 0.5f).SetEase(Ease.InOutQuad);
                cornKernel.transform.DOScale(6f, 0.5f)
                    .SetEase(Ease.InOutQuad)
                    .OnComplete(() => {
                        if (cornKernel != null) cornKernel.SetActive(false);
                        if (CornGenerator.Instance != null)
                        {
                            CornGenerator.Instance.OnKernelPeeled();
                        }
                    });
            });

        // Döndürme animasyonunu ayrı olarak başlat
        cornKernel.transform.DORotate(new Vector3(0, 360, 0), 1f, RotateMode.FastBeyond360)
            .SetEase(Ease.Linear);
    }
}
