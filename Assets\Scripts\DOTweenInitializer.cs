using UnityEngine;
using DG.Tweening;

/// <summary>
/// DOTween'i oyun başlangıcında doğru kapasiteyle başlatır
/// Bu script oyundaki tüm diğer scriptlerden önce çalışmalı
/// </summary>
[DefaultExecutionOrder(-100)]
public class DOTweenInitializer : MonoBehaviour
{
    [Header("DOTween Kapasitesi Ayarları")]
    [Tooltip("Aynı anda çalabilecek maksimum Tweener sayısı")]
    public int tweenersCapacity = 1000;
    
    [Tooltip("Aynı anda çalabilecek maksimum Sequence sayısı")]
    public int sequencesCapacity = 200;
    
    [Header("DOTween Genel Ayarları")]
    [Tooltip("Güvenli mod - performans biraz düşer ama daha güvenli")]
    public bool useSafeMode = true;
    
    [Tooltip("Tween'leri otomatik olarak geri dö<PERSON>ü<PERSON> (GC optimizasyonu)")]
    public bool recycleAllByDefault = false;

    private void Awake()
    {
        InitializeDOTween();
    }

    private void InitializeDOTween()
    {
        // DOTween'i başlat ve kapasiteyi ayarla
        DOTween.Init(recycleAllByDefault, useSafeMode, LogBehaviour.ErrorsOnly)
               .SetCapacity(tweenersCapacity, sequencesCapacity);
        
        Debug.Log($"DOTween başlatıldı - Tweener Kapasitesi: {tweenersCapacity}, Sequence Kapasitesi: {sequencesCapacity}");
    }
}
