using UnityEngine;
using DG.Tweening;
using System.Collections;
using System.Collections.Generic;

public class CornGenerator : Singleton<CornGenerator>
{
    [Header("Corn Generator Ayarlarý")]
    public GameObject cornPrefab;   // Tek mýsýr prefab'ý (içerisinde "Corn_Team" child'ý var)
    public Transform spawnPoint;    // Mýsýrýn spawn animasyonu baþlangýç noktasý
    public Transform targetPoint;   // Mýsýrýn yerleþeceði son konum

    public GameObject currentCorn; // Aktif mısır ko<PERSON>nını temsil eder, TouchController tarafından erişilecek.
    // Her kernel için baþlangýçtaki local position, rotation ve scale bilgilerini saklýyoruz.
    private Dictionary<GameObject, KernelInitialState> initialKernelStates = new Dictionary<GameObject, KernelInitialState>();

    // Soyulan kernel sayýsýný takip ediyoruz.
    private int peeledKernelCount = 0;

    public Transform destroyPoint;
    public bool IsAnimating { get; private set; } = false; // Mısır animasyon durumunu belirtir
    void Start()
    {
        SpawnCorn();
    }

    void SpawnCorn()
    {
        if (currentCorn != null)
        {
            Destroy(currentCorn);
        }
        currentCorn = Instantiate(cornPrefab, targetPoint.position, Quaternion.identity,transform);
        SetupCornKernels();
        //AnimateCornSpawn(currentCorn);
        AnimateSpawn(currentCorn);
        peeledKernelCount = 0;
    }

    // "Corn_Team" altýnda bulunan her kernel'in baþlangýçtaki transform bilgilerini kaydediyoruz.
    void SetupCornKernels()
    {
        Transform cornTeam = currentCorn.transform.Find("Corn_Team");
        if (cornTeam != null)
        {
            initialKernelStates.Clear();
            foreach (Transform kernel in cornTeam)
            {
                KernelInitialState state = new KernelInitialState();
                state.localPosition = kernel.localPosition;
                state.localRotation = kernel.localRotation;
                state.localScale = kernel.localScale;
                initialKernelStates[kernel.gameObject] = state;
            }
        }
    }

    // Bir kernel soyulduðunda çaðrýlýr.
    public void OnKernelPeeled()
    {
        peeledKernelCount++;
        Transform cornTeam = currentCorn.transform.Find("Corn_Team");
        if (cornTeam != null && peeledKernelCount >= cornTeam.childCount)
        {
            IsAnimating = true; // Yok olma animasyonu başlayacak, hemen işaretle.
            AnimateDestroy(currentCorn);
        }
    }

    // Tüm kernel'leri başlangıçtaki durumlarına resetleyip spawn animasyonu oynatır.
    private void ResetCornKernels()
    {
        Transform cornTeam = currentCorn.transform.Find("Corn_Team");
        if (cornTeam != null)
        {
            // Önce tüm kernel'leri resetle
            foreach (Transform kernel in cornTeam)
            {
                if (initialKernelStates.TryGetValue(kernel.gameObject, out KernelInitialState state))
                {
                    kernel.localPosition = state.localPosition;
                    kernel.localRotation = state.localRotation;
                    kernel.localScale = state.localScale;
                    kernel.gameObject.SetActive(true);
                    kernel.GetComponent<Collider>().enabled = true;
                }
            }

            // Optimize edilmiş kernel spawn animasyonu - staggered animation
            int kernelIndex = 0;
            foreach (Transform kernel in cornTeam)
            {
                if (initialKernelStates.TryGetValue(kernel.gameObject, out KernelInitialState state))
                {
                    kernel.localScale = Vector3.zero;
                    // Her kernel için küçük bir gecikme ekle (staggered effect)
                    float delay = kernelIndex * 0.05f; // 50ms gecikme
                    kernel.DOScale(state.localScale, 0.5f)
                        .SetEase(Ease.OutBack)
                        .SetDelay(delay);
                    kernelIndex++;
                }
            }
        }
        peeledKernelCount = 0;
    }

    // Mýsýrýn (tüm korniþ) spawn animasyonunu oynatýr.
    void AnimateCornSpawn(GameObject corn)
    {
        corn.transform.position = spawnPoint.position;
        Sequence seq = DOTween.Sequence();
        seq.Append(corn.transform.DOMove(targetPoint.position, 1.5f).SetEase(Ease.OutQuad));
        // Ek animasyonlar (ölçek, rotasyon vb.) isteðe baðlý eklenebilir.
    }

    void AnimateSpawn(GameObject corn)
    {
        IsAnimating = true; // Animasyon başladı
        // Başlangıçta spawnPoint'in konumunda, küçük ölçekle ve spawnPoint'in rotasyonuyla başla.
        corn.transform.position = spawnPoint.position;
        corn.transform.localScale = Vector3.one * 0.1f;
        corn.transform.rotation = spawnPoint.rotation;

        float duration = 1.5f;
        Vector3 targetRot = new Vector3(120f, corn.transform.rotation.eulerAngles.y, corn.transform.rotation.eulerAngles.z);

        // Optimize edilmiş animasyon - Sequence yerine chain kullan
        corn.transform.DOMove(targetPoint.position, duration)
            .SetEase(Ease.OutQuad)
            .OnComplete(() => IsAnimating = false); // Animasyon bitti

        corn.transform.DOScale(Vector3.one, duration).SetEase(Ease.OutQuad);
        corn.transform.DORotate(targetRot, duration).SetEase(Ease.OutQuad);
    }

    void AnimateDestroy(GameObject corn)
    {
        IsAnimating = true; // Yıkım ve ardından spawn animasyonu başlayacak
        float duration = 1.5f;
        Vector3 targetScale = Vector3.zero;
        Vector3 targetRot = destroyPoint.rotation.eulerAngles;

        // Optimize edilmiş animasyon - Sequence yerine chain kullan
        corn.transform.DOMove(destroyPoint.position, duration)
            .SetEase(Ease.InBack)
            .OnComplete(() =>
            {
                ResetCornKernels();
                AnimateSpawn(corn);
            });

        corn.transform.DOScale(targetScale, duration).SetEase(Ease.InBack);
        corn.transform.DORotate(targetRot, duration).SetEase(Ease.InBack);
    }

    // Tüm kernel'lerin materyalinin rengini değiştirir
    public void SetAllKernelColors(Color color)
    {
        if (currentCorn == null) return;
        Transform cornTeam = currentCorn.transform.Find("Corn_Team");
        if (cornTeam != null)
        {
            foreach (Transform kernel in cornTeam)
            {
                var renderer = kernel.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material.color = color;
                }
            }
        }
    }

    // Smoke VFX'i StartPos'tan EndPos'a animasyonla taşır ve rengi ayarlar
    public void PlaySmokeVfx(Color color)
    {
        if (currentCorn == null) return;
        Transform vfx = currentCorn.transform.Find("Vfx");
        if (vfx == null) return;
        Transform smoke = vfx.Find("Smoke");
        Transform startPos = vfx.Find("StartPos");
        Transform endPos = vfx.Find("EndPos");
        if (smoke == null || startPos == null || endPos == null) return;

        // Başlangıçta smoke'u kapalı yap
        smoke.gameObject.SetActive(false);

        // ParticleSystem'in ana rengini değiştir
        ParticleSystem ps = smoke.GetComponent<ParticleSystem>();
        if (ps != null)
        {
            var main = ps.main;
            color.a = main.startColor.color.a; // mevcut alpha'yı koru
            main.startColor = color;
        }

        smoke.position = startPos.position;
        smoke.rotation = startPos.rotation;
        smoke.gameObject.SetActive(true);
        float duration = 1f;
        smoke.DOMove(endPos.position, duration).SetEase(Ease.OutQuad)
            .OnComplete(() => smoke.gameObject.SetActive(false));
    }
}

public class KernelInitialState
{
    public Vector3 localPosition;
    public Quaternion localRotation;
    public Vector3 localScale;
}
