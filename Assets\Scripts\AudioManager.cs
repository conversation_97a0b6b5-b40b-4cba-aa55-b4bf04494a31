using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class AudioManager : Singleton<AudioManager>
{
    public bool isEnable = true; // Varsay�lan olarak ses a��k
    public List<AudioSource> allAudioSources = new List<AudioSource>();
    public List<AudioSource> audioSourcePool = new List<AudioSource>(); // AudioSource havuzu
    public int initialPoolSize = 10;
    public int maxSimultaneousSounds = 5; // Ayn� anda en fazla ka� ses �alabilir

    private Queue<AudioSource> activeSources = new Queue<AudioSource>(); // �alan sesleri takip eden bir s�ra

    public AudioSource MainSource;
    public AudioSource EffectSource;
    public AudioSource SpecialSource;
    public AudioSource UISource;

    private void Start()
    {
        RefreshAudioSources();
        InitializeAudioSourcePool();
    }

    public void StopSoundButton()
    {
        isEnable = !isEnable;
        UpdateAudioSources();
    }

    private void RefreshAudioSources()
    {
        allAudioSources.Clear();
        allAudioSources.AddRange(FindObjectsOfType<AudioSource>());
        UpdateAudioSources();
    }

    private void UpdateAudioSources()
    {
        foreach (var audioSource in allAudioSources)
        {
            if (audioSource != null)
                audioSource.mute = !isEnable;
        }
    }

    private void InitializeAudioSourcePool()
    {
        for (int i = 0; i < initialPoolSize; i++)
        {
            AudioSource newSource = gameObject.AddComponent<AudioSource>();
            audioSourcePool.Add(newSource);
            allAudioSources.Add(newSource);
        }
    }

    private AudioSource GetAvailableAudioSource()
    {
        foreach (var source in audioSourcePool)
        {
            if (!source.isPlaying)
            {
                return source;
            }
        }

        // E�er havuzda bo� kaynak yoksa, en eski �alan sesi durdurup kullan
        if (activeSources.Count >= maxSimultaneousSounds)
        {
            AudioSource oldestSource = activeSources.Dequeue();
            oldestSource.Stop();
            return oldestSource;
        }

        // Yeni bir kaynak olu�tur
        AudioSource newSource = gameObject.AddComponent<AudioSource>();
        audioSourcePool.Add(newSource);
        allAudioSources.Add(newSource);
        return newSource;
    }

    public void PlaySoundFromPool(string clipName, bool loop)
    {
        if (!isEnable) return;

        AudioClip soundClip = Resources.Load<AudioClip>("Sounds/" + clipName);
        if (soundClip == null) return;

        // Sadece loop olan sesler için çakışma kontrolü yap
        // Kısa efekt sesleri (BubbleBrust gibi) için çakışma kontrolü yapma
        if (loop)
        {
            foreach (var activeSource in activeSources)
            {
                if (activeSource.clip == soundClip && activeSource.isPlaying)
                {
                    return;
                }
            }
        }

        AudioSource newSource = GetAvailableAudioSource();
        newSource.clip = soundClip;
        newSource.loop = loop;
        if (newSource != null && newSource.enabled && newSource.gameObject.activeInHierarchy)
        {
            newSource.Play();
        }
        activeSources.Enqueue(newSource);
    }


    public void RegisterNewAudioSource(AudioSource newSource)
    {
        if (!allAudioSources.Contains(newSource))
        {
            allAudioSources.Add(newSource);
            newSource.mute = !isEnable;
        }
    }

    public void StopSound(AudioSource source)
    {
        if (source != null)
        {
            source.Stop();
            source.clip = null;
            activeSources = new Queue<AudioSource>(activeSources); // Kuyru�u g�ncelle
        }
    }

    public void SetSound(string clipName, bool loop, AudioSource source)
    {
        if (!isEnable || source == null) return;
        var soundClip = Resources.Load<AudioClip>("Sounds/" + clipName);
        source.clip = soundClip;
        source.loop = loop;
        source.Play();
    }

    public void StopAllSound()
    {
        foreach (var audioSource in allAudioSources)
        {
            if (audioSource != null)
                audioSource.Stop();
        }
        activeSources.Clear();
    }
}
