using UnityEngine;
using DG.Tweening;

/// <summary>
/// Mısır oyunu test scripti - DOTween kapasitesi ve ses sistemini test eder
/// </summary>
public class CornGameTester : MonoBehaviour
{
    [Header("Test Ayarları")]
    [Tooltip("Test sırasında kaç tane sahte kernel animasyonu çalıştırılacak")]
    public int testKernelCount = 50;
    
    [Tooltip("Test seslerini çal")]
    public bool testAudio = true;
    
    [Header("Debug Bilgileri")]
    [SerializeField] private int activeTweenCount;
    [SerializeField] private int activeSequenceCount;
    
    private void Start()
    {
        // DOTween durumunu kontrol et
        CheckDOTweenStatus();
    }
    
    private void Update()
    {
        // DOTween istatistiklerini güncelle
        if (DOTween.instance != null)
        {
            activeTweenCount = DOTween.TotalActiveTweens();
            activeSequenceCount = DOTween.TotalActiveSequences();
        }
    }
    
    private void CheckDOTweenStatus()
    {
        if (DOTween.instance != null)
        {
            Debug.Log($"DOTween başarıyla başlatıldı!");
            Debug.Log($"Aktif Tween Sayısı: {DOTween.TotalActiveTweens()}");
            Debug.Log($"Aktif Sequence Sayısı: {DOTween.TotalActiveSequences()}");
        }
        else
        {
            Debug.LogWarning("DOTween henüz başlatılmamış!");
        }
    }
    
    [ContextMenu("Test Kernel Animations")]
    public void TestKernelAnimations()
    {
        Debug.Log($"Test başlatılıyor - {testKernelCount} kernel animasyonu...");
        
        for (int i = 0; i < testKernelCount; i++)
        {
            // Sahte kernel objesi oluştur
            GameObject testKernel = GameObject.CreatePrimitive(PrimitiveType.Cube);
            testKernel.name = $"TestKernel_{i}";
            testKernel.transform.position = Random.insideUnitSphere * 5f;
            
            // Mısır kernel animasyonunu simüle et
            SimulateKernelAnimation(testKernel, i);
        }
        
        Debug.Log($"Test tamamlandı. Aktif Tween: {DOTween.TotalActiveTweens()}, Sequence: {DOTween.TotalActiveSequences()}");
    }
    
    private void SimulateKernelAnimation(GameObject kernel, int index)
    {
        Vector3 originalPos = kernel.transform.position;
        Vector3 targetPos = Vector3.zero;
        
        // Optimize edilmiş animasyon (TouchController'daki gibi)
        kernel.transform.DOJump(originalPos, 2f, 1, 1f)
            .SetEase(Ease.OutQuad)
            .SetDelay(index * 0.1f) // Staggered effect
            .OnComplete(() => {
                kernel.transform.DOMove(targetPos, 0.5f).SetEase(Ease.InOutQuad);
                kernel.transform.DOScale(6f, 0.5f)
                    .SetEase(Ease.InOutQuad)
                    .OnComplete(() => {
                        if (kernel != null) 
                        {
                            Destroy(kernel);
                        }
                    });
            });
        
        // Döndürme animasyonu
        kernel.transform.DORotate(new Vector3(0, 360, 0), 1f, RotateMode.FastBeyond360)
            .SetEase(Ease.Linear)
            .SetDelay(index * 0.1f);
    }
    
    [ContextMenu("Test Audio System")]
    public void TestAudioSystem()
    {
        if (!testAudio) return;
        
        AudioManager audioManager = AudioManager.Instance;
        if (audioManager == null)
        {
            Debug.LogError("AudioManager bulunamadı!");
            return;
        }
        
        Debug.Log("Ses sistemi test ediliyor...");
        
        // Hızlı ardışık ses çalma testi
        for (int i = 0; i < 10; i++)
        {
            audioManager.PlaySoundFromPool("BubbleBrust", false);
        }
        
        Debug.Log("Ses testi tamamlandı - artık sesler kesilmemeli!");
    }
    
    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label($"DOTween İstatistikleri:");
        GUILayout.Label($"Aktif Tween: {activeTweenCount}");
        GUILayout.Label($"Aktif Sequence: {activeSequenceCount}");
        
        if (GUILayout.Button("Kernel Animasyon Testi"))
        {
            TestKernelAnimations();
        }
        
        if (GUILayout.Button("Ses Sistemi Testi"))
        {
            TestAudioSystem();
        }
        
        GUILayout.EndArea();
    }
}
